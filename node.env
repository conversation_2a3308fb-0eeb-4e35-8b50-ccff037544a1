NODE_ENV="development"
PORT="8080"

DATABASE_URL="mongodb+srv://shayanameend:<EMAIL>/ecobuilt"

JWT_SECRET="I8GsKi181/jHsgePzo+bE7P6JSP8aR/MIBTnaL241WI="
JWT_EXPIRY="7d"

NODEMAILER_HOST="smtp.gmail.com"
NODEMAILER_PORT="465"
NODEMAILER_SECURE="true"
NODEMAILER_EMAIL="<EMAIL>"
NODEMAILER_PASSWORD="gbdvxsqokfjfeqwd"

CLIENT_BASE_URL="http://localhost:3000"

APP_NAME="EcoBuilt"
APP_SUPPORT_EMAIL="<EMAIL>"
APP_ADMIN_EMAIL="<EMAIL>"

AWS_ACCESS_KEY_ID="********************"
AWS_SECRET_ACCESS_KEY="jqX+amG1wxRSZT84sA5jD+M1w9ujGD6rB+xN9te3"
AWS_BUCKET="ecobuilt"
AWS_REGION="us-east-1"

PAYSTACK_SECRET_KEY="sk_test_0f9ca49fa68b404a38fe6be191b90e64c4efb46a"
PAYSTACK_PUBLIC_KEY="pk_test_51fefe76901a2ac2caea697efc2ee9ed8887a0a1"
