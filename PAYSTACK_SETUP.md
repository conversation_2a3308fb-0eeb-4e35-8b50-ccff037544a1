# Paystack Integration Setup Guide

## Quick Start

### 1. Environment Setup

#### Backend Environment Variables
Add to `node.env`:
```env
PAYSTACK_SECRET_KEY=sk_test_your_secret_key_here
PAYSTACK_PUBLIC_KEY=pk_test_your_public_key_here
```

#### Frontend Environment Variables
Add to `next.env`:
```env
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_your_public_key_here
```

### 2. Database Migration

Run Prisma migration to create payment tables:
```bash
cd node-js
pnpm prisma:generate
# If using a database migration tool, run migrations
```

### 3. Paystack Dashboard Configuration

1. **Login to Paystack Dashboard**
   - Go to [https://dashboard.paystack.com](https://dashboard.paystack.com)
   - Use your Paystack account credentials

2. **Get API Keys**
   - Navigate to Settings → API Keys & Webhooks
   - Copy your Test Public Key and Test Secret Key
   - For production, use Live keys

3. **Configure Webhooks**
   - Go to Settings → API Keys & Webhooks → Webhooks
   - Add webhook URL: `https://yourdomain.com/payments/webhook`
   - Select these events:
     - `charge.success`
     - `transfer.success`
     - `transfer.failed`
     - `transfer.reversed`

### 4. Testing the Integration

#### Test Payment Flow
1. Start your backend server:
   ```bash
   cd node-js
   pnpm dev
   ```

2. Start your frontend server:
   ```bash
   cd next-js
   pnpm dev
   ```

3. Create a test order and initiate payment
4. Use Paystack test cards for testing:
   - **Successful payment**: `****************`
   - **Failed payment**: `****************` (with CVV 408)

#### Test Vendor Onboarding
1. Login as a vendor
2. Navigate to payment settings
3. Add bank account details
4. Use test bank account numbers

### 5. Webhook Testing (Local Development)

For local webhook testing, use ngrok:

1. **Install ngrok**:
   ```bash
   npm install -g ngrok
   ```

2. **Expose local server**:
   ```bash
   ngrok http 8080
   ```

3. **Update webhook URL** in Paystack dashboard:
   ```
   https://your-ngrok-url.ngrok.io/payments/webhook
   ```

### 6. Production Deployment

#### Environment Variables
- Replace test keys with live keys
- Ensure webhook URL uses HTTPS
- Set up proper error monitoring

#### Security Checklist
- [ ] Use HTTPS for all endpoints
- [ ] Validate webhook signatures
- [ ] Implement rate limiting
- [ ] Set up monitoring and alerts
- [ ] Regular security audits

### 7. Monitoring Setup

#### Key Metrics to Track
- Payment success rate
- Webhook delivery success
- Payout processing time
- Error rates

#### Recommended Tools
- Application monitoring (e.g., Sentry)
- Log aggregation (e.g., LogRocket)
- Uptime monitoring
- Performance monitoring

## API Endpoints Summary

### Payment Flow
```
POST /payments/initialize     # Initialize payment
GET  /payments/verify/:ref    # Verify payment
POST /payments/webhook        # Webhook handler
```

### Vendor Management
```
GET  /vendor/subaccounts/banks           # Get banks
POST /vendor/subaccounts/resolve-account # Resolve account
POST /vendor/subaccounts                 # Create subaccount
GET  /vendor/payments/analytics          # Payment analytics
```

### Admin Dashboard
```
GET  /admin/payments/analytics  # Payment analytics
GET  /admin/payments           # All payments
GET  /admin/payments/payouts   # All payouts
```

## Common Issues and Solutions

### Issue: Webhook not receiving events
**Solution**: 
- Check webhook URL accessibility
- Verify HTTPS configuration
- Check Paystack dashboard webhook logs

### Issue: Payment verification fails
**Solution**:
- Verify API keys are correct
- Check payment reference format
- Ensure order exists in database

### Issue: Vendor payout fails
**Solution**:
- Verify vendor subaccount setup
- Check bank account details
- Ensure sufficient balance in Paystack

### Issue: Duplicate webhook processing
**Solution**:
- Webhook middleware prevents duplicates
- Check WebhookEvent table for processed events

## Support and Resources

- **Paystack Documentation**: [https://paystack.com/docs](https://paystack.com/docs)
- **Paystack Support**: [<EMAIL>](mailto:<EMAIL>)
- **Test Cards**: [https://paystack.com/docs/payments/test-payments](https://paystack.com/docs/payments/test-payments)

## Next Steps

1. **Test thoroughly** in development environment
2. **Set up monitoring** and alerting
3. **Configure production** environment
4. **Train team** on payment flows
5. **Document** any customizations
6. **Plan** for scaling and optimization

---

**Note**: Always test payment flows thoroughly before going live. Use Paystack test environment for all development and testing.
