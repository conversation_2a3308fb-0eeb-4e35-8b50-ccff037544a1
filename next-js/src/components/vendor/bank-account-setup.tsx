"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { z } from "zod";
import axios from "axios";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { routes } from "~/lib/routes";

const bankAccountSchema = z.object({
  accountNumber: z.string().min(1, "Account number is required"),
  bankCode: z.string().min(1, "Please select a bank"),
  bankName: z.string().min(1, "Bank name is required"),
});

type BankAccountForm = z.infer<typeof bankAccountSchema>;

interface Bank {
  id: number;
  name: string;
  code: string;
  country: string;
  currency: string;
}

interface VendorSubaccount {
  id: string;
  subaccountCode: string;
  accountNumber: string;
  bankCode: string;
  bankName: string;
  accountName: string;
  status: string;
}

export function BankAccountSetup() {
  const [isResolving, setIsResolving] = useState(false);
  const [resolvedAccountName, setResolvedAccountName] = useState<string | null>(null);

  const form = useForm<BankAccountForm>({
    resolver: zodResolver(bankAccountSchema),
    defaultValues: {
      accountNumber: "",
      bankCode: "",
      bankName: "",
    },
  });

  // Fetch banks
  const { data: banksData, isLoading: banksLoading } = useQuery({
    queryKey: ["banks"],
    queryFn: async () => {
      const response = await axios.get<{ data: { banks: Bank[] } }>(
        routes.api.vendor.subaccounts.url() + "/banks",
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
          },
        }
      );
      return response.data.data.banks;
    },
  });

  // Fetch existing subaccount
  const { data: subaccountData, isLoading: subaccountLoading } = useQuery({
    queryKey: ["vendor-subaccount"],
    queryFn: async () => {
      try {
        const response = await axios.get<{ data: { vendorSubaccount: VendorSubaccount } }>(
          routes.api.vendor.subaccounts.url(),
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
            },
          }
        );
        return response.data.data.vendorSubaccount;
      } catch (error: any) {
        if (error.response?.status === 404) {
          return null; // No subaccount exists yet
        }
        throw error;
      }
    },
  });

  // Resolve account number
  const resolveAccountMutation = useMutation({
    mutationFn: async ({ accountNumber, bankCode }: { accountNumber: string; bankCode: string }) => {
      const response = await axios.post(
        routes.api.vendor.subaccounts.url() + "/resolve-account",
        { accountNumber, bankCode },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
          },
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      setResolvedAccountName(data.data.accountDetails.account_name);
      toast.success("Account resolved successfully");
    },
    onError: (error: any) => {
      setResolvedAccountName(null);
      toast.error(error.response?.data?.message || "Failed to resolve account");
    },
  });

  // Create/Update subaccount
  const subaccountMutation = useMutation({
    mutationFn: async (data: BankAccountForm) => {
      const url = routes.api.vendor.subaccounts.url();
      const method = subaccountData ? "put" : "post";
      
      const response = await axios[method](url, data, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success(subaccountData ? "Bank account updated successfully" : "Bank account setup successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to setup bank account");
    },
  });

  // Pre-fill form if subaccount exists
  useEffect(() => {
    if (subaccountData) {
      form.setValue("accountNumber", subaccountData.accountNumber);
      form.setValue("bankCode", subaccountData.bankCode);
      form.setValue("bankName", subaccountData.bankName);
      setResolvedAccountName(subaccountData.accountName);
    }
  }, [subaccountData, form]);

  const handleResolveAccount = () => {
    const accountNumber = form.getValues("accountNumber");
    const bankCode = form.getValues("bankCode");

    if (!accountNumber || !bankCode) {
      toast.error("Please enter account number and select a bank");
      return;
    }

    setIsResolving(true);
    resolveAccountMutation.mutate({ accountNumber, bankCode });
    setIsResolving(false);
  };

  const onSubmit = (data: BankAccountForm) => {
    if (!resolvedAccountName) {
      toast.error("Please resolve the account first");
      return;
    }
    subaccountMutation.mutate(data);
  };

  const selectedBank = banksData?.find(bank => bank.code === form.watch("bankCode"));

  useEffect(() => {
    if (selectedBank) {
      form.setValue("bankName", selectedBank.name);
    }
  }, [selectedBank, form]);

  if (banksLoading || subaccountLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {subaccountData ? "Update Bank Account" : "Setup Bank Account"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="bankCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bank</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your bank" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {banksData?.map((bank) => (
                        <SelectItem key={bank.code} value={bank.code}>
                          {bank.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="accountNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Account Number</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter your account number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="button"
              variant="outline"
              onClick={handleResolveAccount}
              disabled={isResolving || resolveAccountMutation.isPending}
              className="w-full"
            >
              {isResolving || resolveAccountMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Resolving...
                </>
              ) : (
                "Verify Account"
              )}
            </Button>

            {resolvedAccountName && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-800">
                  <strong>Account Name:</strong> {resolvedAccountName}
                </p>
              </div>
            )}

            <Button
              type="submit"
              disabled={!resolvedAccountName || subaccountMutation.isPending}
              className="w-full"
            >
              {subaccountMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {subaccountData ? "Updating..." : "Setting up..."}
                </>
              ) : (
                subaccountData ? "Update Bank Account" : "Setup Bank Account"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
