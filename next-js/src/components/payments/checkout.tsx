"use client";

import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { PaystackPayment } from "./paystack-payment";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Separator } from "~/components/ui/separator";
import { toast } from "sonner";
import { routes } from "~/lib/routes";

interface CheckoutProps {
  orderId: string;
  orderTotal: number;
  userEmail: string;
  onPaymentSuccess: () => void;
  onPaymentCancel: () => void;
}

interface PaymentInitResponse {
  data: {
    payment: {
      id: string;
      reference: string;
      amount: number;
    };
    authorizationUrl: string;
    accessCode: string;
    reference: string;
  };
}

export function Checkout({
  orderId,
  orderTotal,
  userEmail,
  onPaymentSuccess,
  onPaymentCancel,
}: CheckoutProps) {
  const [paymentData, setPaymentData] = useState<PaymentInitResponse["data"] | null>(null);

  const initializePaymentMutation = useMutation({
    mutationFn: async () => {
      const response = await axios.post<PaymentInitResponse>(
        routes.api.user.orders.url(orderId) + "/payment",
        {},
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
          },
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      setPaymentData(data.data);
      toast.success("Payment initialized successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to initialize payment");
    },
  });

  const verifyPaymentMutation = useMutation({
    mutationFn: async (reference: string) => {
      const response = await axios.get(
        routes.api.public.payments.url() + `/verify/${reference}`
      );
      return response.data;
    },
    onSuccess: () => {
      toast.success("Payment verified successfully!");
      onPaymentSuccess();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Payment verification failed");
    },
  });

  const handlePaymentSuccess = (reference: any) => {
    verifyPaymentMutation.mutate(reference.reference);
  };

  const handlePaymentCancel = () => {
    setPaymentData(null);
    onPaymentCancel();
  };

  if (!paymentData) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Complete Your Order</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Order Total:</span>
              <span className="font-semibold">R {orderTotal.toFixed(2)}</span>
            </div>
            <Separator />
            <div className="flex justify-between text-lg font-bold">
              <span>Total to Pay:</span>
              <span>R {orderTotal.toFixed(2)}</span>
            </div>
          </div>
          
          <Button
            onClick={() => initializePaymentMutation.mutate()}
            disabled={initializePaymentMutation.isPending}
            className="w-full"
          >
            {initializePaymentMutation.isPending ? "Initializing..." : "Proceed to Payment"}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Pay with Paystack</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Order Total:</span>
            <span className="font-semibold">R {orderTotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Payment Reference:</span>
            <span className="font-mono text-xs">{paymentData.reference}</span>
          </div>
          <Separator />
          <div className="flex justify-between text-lg font-bold">
            <span>Total to Pay:</span>
            <span>R {orderTotal.toFixed(2)}</span>
          </div>
        </div>

        <PaystackPayment
          email={userEmail}
          amount={orderTotal}
          reference={paymentData.reference}
          publicKey={process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY!}
          onSuccess={handlePaymentSuccess}
          onClose={handlePaymentCancel}
          disabled={verifyPaymentMutation.isPending}
        >
          {verifyPaymentMutation.isPending ? "Verifying..." : "Pay Now"}
        </PaystackPayment>

        <Button
          variant="outline"
          onClick={handlePaymentCancel}
          className="w-full"
          disabled={verifyPaymentMutation.isPending}
        >
          Cancel Payment
        </Button>
      </CardContent>
    </Card>
  );
}
