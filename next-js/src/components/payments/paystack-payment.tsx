"use client";

import { useState } from "react";
import { usePaystackPayment } from "react-paystack";
import { Button } from "~/components/ui/button";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

interface PaystackPaymentProps {
  email: string;
  amount: number;
  reference: string;
  publicKey: string;
  onSuccess: (reference: any) => void;
  onClose: () => void;
  disabled?: boolean;
  children?: React.ReactNode;
}

export function PaystackPayment({
  email,
  amount,
  reference,
  publicKey,
  onSuccess,
  onClose,
  disabled = false,
  children,
}: PaystackPaymentProps) {
  const [isLoading, setIsLoading] = useState(false);

  const config = {
    reference,
    email,
    amount: Math.round(amount * 100), // Convert to kobo
    publicKey,
    currency: "ZAR",
  };

  const initializePayment = usePaystackPayment(config);

  const handlePayment = () => {
    if (disabled) return;
    
    setIsLoading(true);
    
    initializePayment({
      onSuccess: (reference) => {
        setIsLoading(false);
        toast.success("Payment successful!");
        onSuccess(reference);
      },
      onClose: () => {
        setIsLoading(false);
        toast.info("Payment cancelled");
        onClose();
      },
    });
  };

  return (
    <Button
      onClick={handlePayment}
      disabled={disabled || isLoading}
      className="w-full"
    >
      {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      {children || "Pay Now"}
    </Button>
  );
}
