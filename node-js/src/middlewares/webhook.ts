import type { NextFunction, Request, Response } from "express";
import crypto from "crypto";
import { prisma } from "~/lib/prisma";

/**
 * Middleware to verify Paystack webhook signature
 */
function verifyPaystackWebhook(
  request: Request,
  response: Response,
  next: NextFunction
): void {
  try {
    const signature = request.headers["x-paystack-signature"] as string;

    if (!signature) {
      console.log("Missing webhook signature");
      response.status(400).json({ message: "Missing signature" });
      return;
    }

    const payload = JSON.stringify(request.body);
    const secretKey = process.env.PAYSTACK_SECRET_KEY;

    if (!secretKey) {
      console.error("PAYSTACK_SECRET_KEY environment variable is not set");
      response.status(500).json({ message: "Server configuration error" });
      return;
    }

    const hash = crypto
      .createHmac("sha512", secretKey)
      .update(payload)
      .digest("hex");

    if (hash !== signature) {
      console.log("Invalid webhook signature");
      response.status(400).json({ message: "Invalid signature" });
      return;
    }

    next();
  } catch (error) {
    console.error("Webhook verification error:", error);
    response.status(500).json({ message: "Webhook verification failed" });
  }
}

/**
 * Middleware to prevent duplicate webhook processing
 */
async function preventDuplicateWebhook(
  request: Request,
  response: Response,
  next: NextFunction
): Promise<void> {
  try {
    const event = request.body;
    const eventId = event.id;

    if (!eventId) {
      console.log("Missing event ID in webhook");
      response.status(400).json({ message: "Missing event ID" });
      return;
    }

    // Check if we've already processed this webhook
    const existingWebhook = await prisma.webhookEvent.findFirst({
      where: { eventId },
    });

    if (existingWebhook) {
      console.log(`Duplicate webhook event: ${eventId}`);
      response.status(200).json({ message: "Event already processed" });
      return;
    }

    // Store the webhook event to prevent duplicates
    await prisma.webhookEvent.create({
      data: {
        eventId,
        eventType: event.event,
        data: event.data,
        processedAt: new Date(),
      },
    });

    next();
  } catch (error) {
    console.error("Duplicate webhook prevention error:", error);
    response.status(500).json({ message: "Webhook processing failed" });
  }
}

/**
 * Middleware to log webhook events
 */
function logWebhookEvent(
  request: Request,
  _response: Response,
  next: NextFunction
): void {
  const event = request.body;
  console.log(`Received webhook event: ${event.event} with ID: ${event.id}`);

  // Log important event details
  if (event.event === "charge.success") {
    console.log(
      `Payment successful: ${event.data.reference} - Amount: ${event.data.amount}`
    );
  } else if (event.event === "transfer.success") {
    console.log(
      `Transfer successful: ${event.data.transfer_code} - Amount: ${event.data.amount}`
    );
  } else if (event.event === "transfer.failed") {
    console.log(
      `Transfer failed: ${event.data.transfer_code} - Reason: ${event.data.failure_reason}`
    );
  }

  next();
}

export { verifyPaystackWebhook, preventDuplicateWebhook, logWebhookEvent };
