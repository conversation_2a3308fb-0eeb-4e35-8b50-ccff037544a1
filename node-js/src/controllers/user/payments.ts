import type { Request, Response } from "express";
import { handleErrors } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { initializePayment } from "~/services/payments";
import { z } from "zod";

const initializeOrderPaymentSchema = z.object({
  id: z.string().length(24, "Order ID must be a 24-character string"),
});

/**
 * Initialize payment for a user's order
 */
async function initializeOrderPayment(request: Request, response: Response) {
  try {
    const { id: orderId } = initializeOrderPaymentSchema.parse(request.params);

    // Get the order and verify it belongs to the user
    const order = await prisma.order.findFirst({
      where: {
        id: orderId,
        user: {
          authId: request.user.id,
        },
      },
      include: {
        user: {
          include: {
            auth: true,
          },
        },
        payment: true,
      },
    });

    if (!order) {
      return response.notFound(
        {},
        {
          message: "Order not found",
        }
      );
    }

    if (order.payment) {
      return response.badRequest(
        {},
        {
          message: "Payment already exists for this order",
        }
      );
    }

    if (order.status !== "PENDING") {
      return response.badRequest(
        {},
        {
          message: "Order is not in pending status",
        }
      );
    }

    const result = await initializePayment({
      orderId,
      email: order.user.auth.email,
      amount: order.totalPrice,
    });

    return response.success(
      {
        data: {
          payment: result.payment,
          authorizationUrl: result.authorizationUrl,
          accessCode: result.accessCode,
          reference: result.reference,
        },
      },
      {
        message: "Payment initialized successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { initializeOrderPayment };
