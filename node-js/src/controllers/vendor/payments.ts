import type { Request, Response } from "express";
import { handleErrors } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import {
  getVendorPaymentAnalytics,
  getVendorPayouts,
  getVendorPaymentHistory,
  getVendorPayoutDetails,
} from "~/services/vendor/payments";
import { z } from "zod";

const getPayoutsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  status: z.enum(["PENDING", "SUCCESS", "FAILED", "REVERSED"]).optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
});

const getPaymentHistoryQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  status: z.enum(["PENDING", "SUCCESS", "FAILED", "ABANDONED"]).optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
});

const payoutParamsSchema = z.object({
  id: z.string().length(24, "Payout ID must be a 24-character string"),
});

/**
 * Get payment analytics for vendor dashboard
 */
async function getAnalytics(request: Request, response: Response) {
  try {
    // Get vendor ID from authenticated user
    const vendor = await prisma.vendor.findUnique({
      where: { authId: request.user.id },
      select: { id: true },
    });

    if (!vendor) {
      return response.badRequest(
        {},
        {
          message: "Vendor not found",
        }
      );
    }

    const analytics = await getVendorPaymentAnalytics(vendor.id);

    return response.success(
      {
        data: { analytics },
      },
      {
        message: "Payment analytics fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get vendor's payouts with pagination
 */
async function getPayoutsController(request: Request, response: Response) {
  try {
    const { page, limit, status, startDate, endDate } = getPayoutsQuerySchema.parse(
      request.query
    );

    // Get vendor ID from authenticated user
    const vendor = await prisma.vendor.findUnique({
      where: { authId: request.user.id },
      select: { id: true },
    });

    if (!vendor) {
      return response.badRequest(
        {},
        {
          message: "Vendor not found",
        }
      );
    }

    const result = await getVendorPayouts({
      vendorId: vendor.id,
      page,
      limit,
      status,
      startDate,
      endDate,
    });

    return response.success(
      {
        data: { payouts: result.payouts },
        meta: {
          total: result.total,
          pages: result.pages,
          limit: result.limit,
          page: result.page,
        },
      },
      {
        message: "Payouts fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get vendor's payment history
 */
async function getPaymentHistoryController(request: Request, response: Response) {
  try {
    const { page, limit, status, startDate, endDate } = getPaymentHistoryQuerySchema.parse(
      request.query
    );

    // Get vendor ID from authenticated user
    const vendor = await prisma.vendor.findUnique({
      where: { authId: request.user.id },
      select: { id: true },
    });

    if (!vendor) {
      return response.badRequest(
        {},
        {
          message: "Vendor not found",
        }
      );
    }

    const result = await getVendorPaymentHistory({
      vendorId: vendor.id,
      page,
      limit,
      status,
      startDate,
      endDate,
    });

    return response.success(
      {
        data: { orders: result.orders },
        meta: {
          total: result.total,
          pages: result.pages,
          limit: result.limit,
          page: result.page,
        },
      },
      {
        message: "Payment history fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get payout details by ID
 */
async function getPayoutDetailsController(request: Request, response: Response) {
  try {
    const { id } = payoutParamsSchema.parse(request.params);

    // Get vendor ID from authenticated user
    const vendor = await prisma.vendor.findUnique({
      where: { authId: request.user.id },
      select: { id: true },
    });

    if (!vendor) {
      return response.badRequest(
        {},
        {
          message: "Vendor not found",
        }
      );
    }

    const result = await getVendorPayoutDetails(vendor.id, id);

    return response.success(
      {
        data: { payout: result.payout },
      },
      {
        message: "Payout details fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export {
  getAnalytics,
  getPayoutsController,
  getPaymentHistoryController,
  getPayoutDetailsController,
};
