import type { Request, Response } from "express";
import { handleErrors } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import {
  getBanks,
  resolveAccountNumber,
  createVendorSubaccount,
  updateVendorSubaccount,
  getVendorSubaccount,
  deactivateVendorSubaccount,
} from "~/services/vendor-subaccounts";
import { z } from "zod";

const createSubaccountSchema = z.object({
  accountNumber: z.string().min(1, "Account number is required"),
  bankCode: z.string().min(1, "Bank code is required"),
  bankName: z.string().min(1, "Bank name is required"),
});

const resolveAccountSchema = z.object({
  accountNumber: z.string().min(1, "Account number is required"),
  bankCode: z.string().min(1, "Bank code is required"),
});

/**
 * Get list of South African banks
 */
async function getBanksController(request: Request, response: Response) {
  try {
    const banks = await getBanks();

    return response.success(
      {
        data: { banks },
      },
      {
        message: "Banks fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Resolve account number to get account name
 */
async function resolveAccountController(request: Request, response: Response) {
  try {
    const { accountNumber, bankCode } = resolveAccountSchema.parse(
      request.body
    );

    const accountDetails = await resolveAccountNumber({
      accountNumber,
      bankCode,
    });

    return response.success(
      {
        data: { accountDetails },
      },
      {
        message: "Account resolved successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Create vendor subaccount
 */
async function createSubaccountController(
  request: Request,
  response: Response
) {
  try {
    const { accountNumber, bankCode, bankName } = createSubaccountSchema.parse(
      request.body
    );

    // Get vendor ID from authenticated user
    const vendor = await prisma.vendor.findUnique({
      where: { authId: request.user.id },
      select: { id: true },
    });

    if (!vendor) {
      return response.badRequest(
        {},
        {
          message: "Vendor not found",
        }
      );
    }

    const result = await createVendorSubaccount({
      vendorId: vendor.id,
      accountNumber,
      bankCode,
      bankName,
    });

    return response.success(
      {
        data: {
          vendorSubaccount: result.vendorSubaccount,
        },
      },
      {
        message: "Vendor subaccount created successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Update vendor subaccount
 */
async function updateSubaccountController(
  request: Request,
  response: Response
) {
  try {
    const { accountNumber, bankCode, bankName } = createSubaccountSchema.parse(
      request.body
    );

    // Get vendor ID from authenticated user
    const vendor = await prisma.vendor.findUnique({
      where: { authId: request.user.id },
      select: { id: true },
    });

    if (!vendor) {
      return response.badRequest(
        {},
        {
          message: "Vendor not found",
        }
      );
    }

    const result = await updateVendorSubaccount({
      vendorId: vendor.id,
      accountNumber,
      bankCode,
      bankName,
    });

    return response.success(
      {
        data: {
          vendorSubaccount: result.vendorSubaccount,
        },
      },
      {
        message: "Vendor subaccount updated successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get vendor subaccount details
 */
async function getSubaccountController(request: Request, response: Response) {
  try {
    // Get vendor ID from authenticated user
    const vendor = await prisma.vendor.findUnique({
      where: { authId: request.user.id },
      select: { id: true },
    });

    if (!vendor) {
      return response.badRequest(
        {},
        {
          message: "Vendor not found",
        }
      );
    }

    const result = await getVendorSubaccount(vendor.id);

    return response.success(
      {
        data: {
          vendorSubaccount: result.vendorSubaccount,
        },
      },
      {
        message: "Vendor subaccount fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Deactivate vendor subaccount
 */
async function deactivateSubaccountController(
  request: Request,
  response: Response
) {
  try {
    // Get vendor ID from authenticated user
    const vendor = await prisma.vendor.findUnique({
      where: { authId: request.user.id },
      select: { id: true },
    });

    if (!vendor) {
      return response.badRequest(
        {},
        {
          message: "Vendor not found",
        }
      );
    }

    const result = await deactivateVendorSubaccount(vendor.id);

    return response.success(
      {
        data: {
          vendorSubaccount: result.vendorSubaccount,
        },
      },
      {
        message: "Vendor subaccount deactivated successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export {
  getBanksController,
  resolveAccountController,
  createSubaccountController,
  updateSubaccountController,
  getSubaccountController,
  deactivateSubaccountController,
};
