import type { Request, Response } from "express";
import { handleErrors } from "~/lib/error";
import {
  initializePayment,
  verifyPayment,
  handleWebhookEvent,
} from "~/services/payments";
import { z } from "zod";

const initializePaymentSchema = z.object({
  orderId: z.string().length(24, "Order ID must be a 24-character string"),
  email: z.string().email("Invalid email address"),
  amount: z.number().positive("Amount must be positive"),
});

const verifyPaymentSchema = z.object({
  reference: z.string().min(1, "Reference is required"),
});

/**
 * Initialize payment for an order
 */
async function initializePaymentController(
  request: Request,
  response: Response
) {
  try {
    const { orderId, email, amount } = initializePaymentSchema.parse(
      request.body
    );

    const result = await initializePayment({
      orderId,
      email,
      amount,
    });

    return response.success(
      {
        data: {
          payment: result.payment,
          authorizationUrl: result.authorizationUrl,
          accessCode: result.accessCode,
          reference: result.reference,
        },
      },
      {
        message: "Payment initialized successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Verify payment status
 */
async function verifyPaymentController(request: Request, response: Response) {
  try {
    const { reference } = verifyPaymentSchema.parse(request.params);

    const result = await verifyPayment(reference);

    return response.success(
      {
        data: {
          payment: result.payment,
          transactionData: result.transactionData,
        },
      },
      {
        message: "Payment verified successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Handle Paystack webhooks
 * Note: Signature verification and duplicate prevention are handled by middleware
 */
async function webhookController(
  request: Request,
  response: Response
): Promise<void> {
  try {
    // Acknowledge receipt of the event first
    response.status(200).json({ message: "Webhook received" });

    // Handle the event asynchronously
    const event = request.body;

    try {
      await handleWebhookEvent(event);
      console.log(
        `Successfully processed webhook event: ${event.event} with ID: ${event.id}`
      );
    } catch (eventError) {
      console.error(
        `Failed to process webhook event: ${event.event} with ID: ${event.id}`,
        eventError
      );
      // Don't throw here as we've already acknowledged the webhook
    }
  } catch (error) {
    console.error("Webhook controller error:", error);
    response.status(500).json({ message: "Webhook processing failed" });
  }
}

export {
  initializePaymentController,
  verifyPaymentController,
  webhookController,
};
