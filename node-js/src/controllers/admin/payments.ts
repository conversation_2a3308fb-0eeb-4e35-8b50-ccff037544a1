import type { Request, Response } from "express";
import { handleErrors } from "~/lib/error";
import {
  getPaymentAnalytics,
  getPayments,
  getPayouts,
  getPaymentDetails,
  triggerManualPayout,
} from "~/services/admin/payments";
import { z } from "zod";

const getPaymentsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  status: z.enum(["PENDING", "SUCCESS", "FAILED", "ABANDONED"]).optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
});

const getPayoutsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  status: z.enum(["PENDING", "SUCCESS", "FAILED", "REVERSED"]).optional(),
  vendorId: z.string().length(24).optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
});

const paymentParamsSchema = z.object({
  id: z.string().length(24, "Payment ID must be a 24-character string"),
});

/**
 * Get payment analytics for admin dashboard
 */
async function getAnalytics(request: Request, response: Response) {
  try {
    const analytics = await getPaymentAnalytics();

    return response.success(
      {
        data: { analytics },
      },
      {
        message: "Payment analytics fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get all payments with pagination and filters
 */
async function getPaymentsController(request: Request, response: Response) {
  try {
    const { page, limit, status, startDate, endDate } = getPaymentsQuerySchema.parse(
      request.query
    );

    const result = await getPayments({
      page,
      limit,
      status,
      startDate,
      endDate,
    });

    return response.success(
      {
        data: { payments: result.payments },
        meta: {
          total: result.total,
          pages: result.pages,
          limit: result.limit,
          page: result.page,
        },
      },
      {
        message: "Payments fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get all payouts with pagination and filters
 */
async function getPayoutsController(request: Request, response: Response) {
  try {
    const { page, limit, status, vendorId, startDate, endDate } = getPayoutsQuerySchema.parse(
      request.query
    );

    const result = await getPayouts({
      page,
      limit,
      status,
      vendorId,
      startDate,
      endDate,
    });

    return response.success(
      {
        data: { payouts: result.payouts },
        meta: {
          total: result.total,
          pages: result.pages,
          limit: result.limit,
          page: result.page,
        },
      },
      {
        message: "Payouts fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get payment details by ID
 */
async function getPaymentDetailsController(request: Request, response: Response) {
  try {
    const { id } = paymentParamsSchema.parse(request.params);

    const result = await getPaymentDetails(id);

    return response.success(
      {
        data: { payment: result.payment },
      },
      {
        message: "Payment details fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Manually trigger payout for a payment
 */
async function triggerPayoutController(request: Request, response: Response) {
  try {
    const { id } = paymentParamsSchema.parse(request.params);

    const result = await triggerManualPayout(id);

    return response.success(
      {
        data: { payout: result.payout },
      },
      {
        message: "Payout triggered successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export {
  getAnalytics,
  getPaymentsController,
  getPayoutsController,
  getPaymentDetailsController,
  triggerPayoutController,
};
