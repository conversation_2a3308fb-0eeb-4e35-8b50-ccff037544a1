import { paystackClient } from "./paystack";
import { prisma } from "~/lib/prisma";
import { BadResponse, NotFoundResponse } from "~/lib/error";

/**
 * Get list of South African banks from Paystack
 */
async function getBanks() {
  try {
    const response = await paystackClient.get("/bank", {
      params: {
        country: "south-africa",
        currency: "ZAR",
      },
    });

    if (!response.data.status) {
      throw new BadResponse("Failed to fetch banks");
    }

    return response.data.data;
  } catch (error) {
    console.error("Error fetching banks:", error);
    throw error;
  }
}

/**
 * Resolve account number to get account name
 */
async function resolveAccountNumber({
  accountNumber,
  bankCode,
}: {
  accountNumber: string;
  bankCode: string;
}) {
  try {
    const response = await paystackClient.get("/bank/resolve", {
      params: {
        account_number: accountNumber,
        bank_code: bankCode,
      },
    });

    if (!response.data.status) {
      throw new BadResponse("Failed to resolve account number");
    }

    return response.data.data;
  } catch (error) {
    console.error("Error resolving account number:", error);
    throw error;
  }
}

/**
 * Create transfer recipient (subaccount) for vendor
 */
async function createVendorSubaccount({
  vendorId,
  accountNumber,
  bankCode,
  bankName,
}: {
  vendorId: string;
  accountNumber: string;
  bankCode: string;
  bankName: string;
}) {
  try {
    // Check if vendor exists
    const vendor = await prisma.vendor.findUnique({
      where: { id: vendorId },
      include: {
        vendorSubaccount: true,
        auth: true,
      },
    });

    if (!vendor) {
      throw new NotFoundResponse("Vendor not found");
    }

    if (vendor.vendorSubaccount) {
      throw new BadResponse("Vendor already has a subaccount");
    }

    // Resolve account number to get account name
    const accountDetails = await resolveAccountNumber({
      accountNumber,
      bankCode,
    });

    // Create transfer recipient on Paystack
    const params = {
      type: "nuban", // Standard type for South African accounts
      name: accountDetails.account_name,
      account_number: accountNumber,
      bank_code: bankCode,
      currency: "ZAR",
      description: `Transfer recipient for vendor: ${vendor.name}`,
      metadata: {
        vendor_id: vendorId,
        vendor_name: vendor.name,
        vendor_email: vendor.auth.email,
      },
    };

    const response = await paystackClient.post("/transferrecipient", params);

    if (!response.data.status) {
      throw new BadResponse("Failed to create transfer recipient");
    }

    const recipientData = response.data.data;

    // Save subaccount details to database
    const vendorSubaccount = await prisma.vendorSubaccount.create({
      data: {
        vendorId,
        subaccountCode: recipientData.recipient_code,
        accountNumber,
        bankCode,
        bankName,
        accountName: accountDetails.account_name,
        status: "ACTIVE",
      },
    });

    return {
      vendorSubaccount,
      recipientData,
    };
  } catch (error) {
    console.error("Error creating vendor subaccount:", error);
    throw error;
  }
}

/**
 * Update vendor subaccount
 */
async function updateVendorSubaccount({
  vendorId,
  accountNumber,
  bankCode,
  bankName,
}: {
  vendorId: string;
  accountNumber: string;
  bankCode: string;
  bankName: string;
}) {
  try {
    const vendor = await prisma.vendor.findUnique({
      where: { id: vendorId },
      include: {
        vendorSubaccount: true,
        auth: true,
      },
    });

    if (!vendor) {
      throw new NotFoundResponse("Vendor not found");
    }

    if (!vendor.vendorSubaccount) {
      throw new NotFoundResponse("Vendor subaccount not found");
    }

    // Resolve account number to get account name
    const accountDetails = await resolveAccountNumber({
      accountNumber,
      bankCode,
    });

    // Update transfer recipient on Paystack
    const params = {
      name: accountDetails.account_name,
      email: vendor.auth.email,
    };

    const response = await paystackClient.put(
      `/transferrecipient/${vendor.vendorSubaccount.subaccountCode}`,
      params
    );

    if (!response.data.status) {
      throw new BadResponse("Failed to update transfer recipient");
    }

    // Update subaccount details in database
    const updatedSubaccount = await prisma.vendorSubaccount.update({
      where: { vendorId },
      data: {
        accountNumber,
        bankCode,
        bankName,
        accountName: accountDetails.account_name,
      },
    });

    return {
      vendorSubaccount: updatedSubaccount,
      recipientData: response.data.data,
    };
  } catch (error) {
    console.error("Error updating vendor subaccount:", error);
    throw error;
  }
}

/**
 * Get vendor subaccount details
 */
async function getVendorSubaccount(vendorId: string) {
  try {
    const vendorSubaccount = await prisma.vendorSubaccount.findUnique({
      where: { vendorId },
      include: {
        vendor: {
          include: {
            auth: true,
          },
        },
      },
    });

    if (!vendorSubaccount) {
      throw new NotFoundResponse("Vendor subaccount not found");
    }

    return { vendorSubaccount };
  } catch (error) {
    console.error("Error fetching vendor subaccount:", error);
    throw error;
  }
}

/**
 * Deactivate vendor subaccount
 */
async function deactivateVendorSubaccount(vendorId: string) {
  try {
    const vendorSubaccount = await prisma.vendorSubaccount.findUnique({
      where: { vendorId },
    });

    if (!vendorSubaccount) {
      throw new NotFoundResponse("Vendor subaccount not found");
    }

    // Update status in database
    const updatedSubaccount = await prisma.vendorSubaccount.update({
      where: { vendorId },
      data: {
        status: "INACTIVE",
      },
    });

    return { vendorSubaccount: updatedSubaccount };
  } catch (error) {
    console.error("Error deactivating vendor subaccount:", error);
    throw error;
  }
}

export {
  getBanks,
  resolveAccountNumber,
  createVendorSubaccount,
  updateVendorSubaccount,
  getVendorSubaccount,
  deactivateVendorSubaccount,
};
