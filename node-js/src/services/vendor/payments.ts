import { prisma } from "~/lib/prisma";
import { NotFoundResponse } from "~/lib/error";

/**
 * Get payment analytics for vendor dashboard
 */
async function getVendorPaymentAnalytics(vendorId: string) {
  try {
    const [
      totalEarnings,
      pendingPayouts,
      successfulPayouts,
      totalOrders,
      paidOrders,
    ] = await Promise.all([
      // Total earnings from successful payments
      prisma.payment.aggregate({
        where: {
          status: "SUCCESS",
          order: {
            orderToProduct: {
              some: {
                product: {
                  vendorId,
                },
              },
            },
          },
        },
        _sum: { amount: true },
      }),
      
      // Pending payouts amount
      prisma.payout.aggregate({
        where: {
          vendorId,
          status: "PENDING",
        },
        _sum: { amount: true },
      }),
      
      // Successful payouts amount
      prisma.payout.aggregate({
        where: {
          vendorId,
          status: "SUCCESS",
        },
        _sum: { amount: true },
      }),
      
      // Total orders count
      prisma.order.count({
        where: {
          orderToProduct: {
            some: {
              product: {
                vendorId,
              },
            },
          },
        },
      }),
      
      // Paid orders count
      prisma.order.count({
        where: {
          payment: {
            status: "SUCCESS",
          },
          orderToProduct: {
            some: {
              product: {
                vendorId,
              },
            },
          },
        },
      }),
    ]);

    const totalEarningsAmount = totalEarnings._sum.amount || 0;
    const pendingPayoutsAmount = pendingPayouts._sum.amount || 0;
    const successfulPayoutsAmount = successfulPayouts._sum.amount || 0;
    const expectedPayouts = totalEarningsAmount * 0.9; // 90% of total earnings

    return {
      earnings: {
        total: totalEarningsAmount,
        expected: expectedPayouts,
        received: successfulPayoutsAmount,
        pending: pendingPayoutsAmount,
      },
      orders: {
        total: totalOrders,
        paid: paidOrders,
      },
    };
  } catch (error) {
    console.error("Error fetching vendor payment analytics:", error);
    throw error;
  }
}

/**
 * Get vendor's payouts with pagination
 */
async function getVendorPayouts({
  vendorId,
  page,
  limit,
  status,
  startDate,
  endDate,
}: {
  vendorId: string;
  page: number;
  limit: number;
  status?: string;
  startDate?: Date;
  endDate?: Date;
}) {
  try {
    const where: any = { vendorId };

    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [payouts, total] = await Promise.all([
      prisma.payout.findMany({
        where,
        take: limit,
        skip: (page - 1) * limit,
        orderBy: { createdAt: "desc" },
        include: {
          payment: {
            include: {
              order: {
                select: {
                  id: true,
                  totalPrice: true,
                  createdAt: true,
                },
              },
            },
          },
        },
      }),
      prisma.payout.count({ where }),
    ]);

    const pages = Math.ceil(total / limit);

    return {
      payouts,
      total,
      pages,
      limit,
      page,
    };
  } catch (error) {
    console.error("Error fetching vendor payouts:", error);
    throw error;
  }
}

/**
 * Get vendor's payment history (orders with payments)
 */
async function getVendorPaymentHistory({
  vendorId,
  page,
  limit,
  status,
  startDate,
  endDate,
}: {
  vendorId: string;
  page: number;
  limit: number;
  status?: string;
  startDate?: Date;
  endDate?: Date;
}) {
  try {
    const where: any = {
      orderToProduct: {
        some: {
          product: {
            vendorId,
          },
        },
      },
    };

    if (status) {
      where.payment = {
        status,
      };
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        take: limit,
        skip: (page - 1) * limit,
        orderBy: { createdAt: "desc" },
        include: {
          payment: true,
          user: {
            select: {
              name: true,
              auth: {
                select: {
                  email: true,
                },
              },
            },
          },
          orderToProduct: {
            include: {
              product: {
                select: {
                  name: true,
                  price: true,
                },
              },
            },
          },
        },
      }),
      prisma.order.count({ where }),
    ]);

    const pages = Math.ceil(total / limit);

    return {
      orders,
      total,
      pages,
      limit,
      page,
    };
  } catch (error) {
    console.error("Error fetching vendor payment history:", error);
    throw error;
  }
}

/**
 * Get payout details by ID (for vendor)
 */
async function getVendorPayoutDetails(vendorId: string, payoutId: string) {
  try {
    const payout = await prisma.payout.findFirst({
      where: {
        id: payoutId,
        vendorId,
      },
      include: {
        payment: {
          include: {
            order: {
              include: {
                user: {
                  select: {
                    name: true,
                    auth: {
                      select: {
                        email: true,
                      },
                    },
                  },
                },
                orderToProduct: {
                  include: {
                    product: {
                      select: {
                        name: true,
                        price: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!payout) {
      throw new NotFoundResponse("Payout not found");
    }

    return { payout };
  } catch (error) {
    console.error("Error fetching vendor payout details:", error);
    throw error;
  }
}

export {
  getVendorPaymentAnalytics,
  getVendorPayouts,
  getVendorPaymentHistory,
  getVendorPayoutDetails,
};
