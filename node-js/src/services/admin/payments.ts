import { prisma } from "~/lib/prisma";
import { BadResponse, NotFoundResponse } from "~/lib/error";

/**
 * Get payment analytics for admin dashboard
 */
async function getPaymentAnalytics() {
  try {
    const [
      totalPayments,
      successfulPayments,
      pendingPayments,
      failedPayments,
      totalRevenue,
      totalPayouts,
      pendingPayouts,
    ] = await Promise.all([
      // Total payments count
      prisma.payment.count(),
      
      // Successful payments count
      prisma.payment.count({
        where: { status: "SUCCESS" },
      }),
      
      // Pending payments count
      prisma.payment.count({
        where: { status: "PENDING" },
      }),
      
      // Failed payments count
      prisma.payment.count({
        where: { status: "FAILED" },
      }),
      
      // Total revenue (successful payments)
      prisma.payment.aggregate({
        where: { status: "SUCCESS" },
        _sum: { amount: true },
      }),
      
      // Total payouts amount
      prisma.payout.aggregate({
        where: { status: "SUCCESS" },
        _sum: { amount: true },
      }),
      
      // Pending payouts count
      prisma.payout.count({
        where: { status: "PENDING" },
      }),
    ]);

    const adminCommission = (totalRevenue._sum.amount || 0) - (totalPayouts._sum.amount || 0);

    return {
      payments: {
        total: totalPayments,
        successful: successfulPayments,
        pending: pendingPayments,
        failed: failedPayments,
      },
      revenue: {
        total: totalRevenue._sum.amount || 0,
        adminCommission,
        vendorPayouts: totalPayouts._sum.amount || 0,
      },
      payouts: {
        pending: pendingPayouts,
      },
    };
  } catch (error) {
    console.error("Error fetching payment analytics:", error);
    throw error;
  }
}

/**
 * Get all payments with pagination and filters
 */
async function getPayments({
  page,
  limit,
  status,
  startDate,
  endDate,
}: {
  page: number;
  limit: number;
  status?: string;
  startDate?: Date;
  endDate?: Date;
}) {
  try {
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where,
        take: limit,
        skip: (page - 1) * limit,
        orderBy: { createdAt: "desc" },
        include: {
          order: {
            include: {
              user: {
                include: {
                  auth: {
                    select: {
                      email: true,
                    },
                  },
                },
              },
              orderToProduct: {
                include: {
                  product: {
                    include: {
                      vendor: {
                        select: {
                          id: true,
                          name: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          payouts: true,
        },
      }),
      prisma.payment.count({ where }),
    ]);

    const pages = Math.ceil(total / limit);

    return {
      payments,
      total,
      pages,
      limit,
      page,
    };
  } catch (error) {
    console.error("Error fetching payments:", error);
    throw error;
  }
}

/**
 * Get all payouts with pagination and filters
 */
async function getPayouts({
  page,
  limit,
  status,
  vendorId,
  startDate,
  endDate,
}: {
  page: number;
  limit: number;
  status?: string;
  vendorId?: string;
  startDate?: Date;
  endDate?: Date;
}) {
  try {
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (vendorId) {
      where.vendorId = vendorId;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [payouts, total] = await Promise.all([
      prisma.payout.findMany({
        where,
        take: limit,
        skip: (page - 1) * limit,
        orderBy: { createdAt: "desc" },
        include: {
          vendor: {
            select: {
              id: true,
              name: true,
              auth: {
                select: {
                  email: true,
                },
              },
            },
          },
          payment: {
            include: {
              order: {
                select: {
                  id: true,
                  totalPrice: true,
                },
              },
            },
          },
        },
      }),
      prisma.payout.count({ where }),
    ]);

    const pages = Math.ceil(total / limit);

    return {
      payouts,
      total,
      pages,
      limit,
      page,
    };
  } catch (error) {
    console.error("Error fetching payouts:", error);
    throw error;
  }
}

/**
 * Get payment details by ID
 */
async function getPaymentDetails(paymentId: string) {
  try {
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        order: {
          include: {
            user: {
              include: {
                auth: {
                  select: {
                    email: true,
                  },
                },
              },
            },
            orderToProduct: {
              include: {
                product: {
                  include: {
                    vendor: {
                      select: {
                        id: true,
                        name: true,
                        auth: {
                          select: {
                            email: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        payouts: {
          include: {
            vendor: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!payment) {
      throw new NotFoundResponse("Payment not found");
    }

    return { payment };
  } catch (error) {
    console.error("Error fetching payment details:", error);
    throw error;
  }
}

/**
 * Manually trigger payout for a payment (admin action)
 */
async function triggerManualPayout(paymentId: string) {
  try {
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        payouts: true,
      },
    });

    if (!payment) {
      throw new NotFoundResponse("Payment not found");
    }

    if (payment.status !== "SUCCESS") {
      throw new BadResponse("Payment must be successful to trigger payout");
    }

    if (payment.payouts.length > 0) {
      throw new BadResponse("Payout already exists for this payment");
    }

    // Import the payout function to avoid circular dependency
    const { initiatePayout } = await import("~/services/payments");
    const result = await initiatePayout(paymentId);

    return result;
  } catch (error) {
    console.error("Error triggering manual payout:", error);
    throw error;
  }
}

export {
  getPaymentAnalytics,
  getPayments,
  getPayouts,
  getPaymentDetails,
  triggerManualPayout,
};
