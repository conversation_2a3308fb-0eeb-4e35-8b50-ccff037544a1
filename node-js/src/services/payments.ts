import crypto from "crypto";
import { paystackClient } from "./paystack";
import { prisma } from "~/lib/prisma";
import { BadResponse, NotFoundResponse } from "~/lib/error";

/**
 * Initialize payment with Paystack
 */
async function initializePayment({
  orderId,
  email,
  amount,
}: {
  orderId: string;
  email: string;
  amount: number;
}) {
  try {
    // Check if order exists and doesn't already have a payment
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        payment: true,
        user: {
          include: {
            auth: true,
          },
        },
        orderToProduct: {
          include: {
            product: {
              include: {
                vendor: true,
              },
            },
          },
        },
      },
    });

    if (!order) {
      throw new NotFoundResponse("Order not found");
    }

    if (order.payment) {
      throw new BadResponse("Payment already exists for this order");
    }

    // Generate unique reference
    const reference = `ecobuilt_${orderId}_${Date.now()}`;

    // Convert amount to kobo (Paystack expects amount in lowest currency unit)
    const amountInKobo = Math.round(amount * 100);

    const params = {
      email,
      amount: amountInKobo,
      currency: "ZAR",
      reference,
      callback_url: `${process.env.CLIENT_BASE_URL}/payment/callback`,
      metadata: {
        order_id: orderId,
        custom_fields: [
          {
            display_name: "Order ID",
            variable_name: "order_id",
            value: orderId,
          },
        ],
      },
    };

    const paystackResponse = await paystackClient.post(
      "/transaction/initialize",
      params
    );

    if (!paystackResponse.data.status) {
      throw new BadResponse("Failed to initialize payment");
    }

    // Create payment record in database
    const payment = await prisma.payment.create({
      data: {
        reference,
        amount,
        orderId,
        accessCode: paystackResponse.data.data.access_code,
        authorizationUrl: paystackResponse.data.data.authorization_url,
      },
    });

    return {
      payment,
      authorizationUrl: paystackResponse.data.data.authorization_url,
      accessCode: paystackResponse.data.data.access_code,
      reference,
    };
  } catch (error) {
    console.error("Error initializing payment:", error);
    throw error;
  }
}

/**
 * Verify payment with Paystack
 */
async function verifyPayment(reference: string) {
  try {
    const paystackResponse = await paystackClient.get(
      `/transaction/verify/${reference}`
    );

    if (!paystackResponse.data.status) {
      throw new BadResponse("Failed to verify payment");
    }

    const transactionData = paystackResponse.data.data;

    // Update payment record
    const payment = await prisma.payment.update({
      where: { reference },
      data: {
        status: transactionData.status === "success" ? "SUCCESS" : "FAILED",
        paystackReference: transactionData.reference,
        paidAt: transactionData.status === "success" ? new Date() : null,
      },
      include: {
        order: {
          include: {
            orderToProduct: {
              include: {
                product: {
                  include: {
                    vendor: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // If payment is successful, update order status and initiate payout
    if (transactionData.status === "success") {
      await prisma.order.update({
        where: { id: payment.orderId },
        data: { status: "PROCESSING" },
      });

      // Initiate payout to vendor (90% of payment amount)
      await initiatePayout(payment.id);
    }

    return { payment, transactionData };
  } catch (error) {
    console.error("Error verifying payment:", error);
    throw error;
  }
}

/**
 * Initiate payout to vendor (90% of payment amount)
 */
async function initiatePayout(paymentId: string) {
  try {
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        order: {
          include: {
            orderToProduct: {
              include: {
                product: {
                  include: {
                    vendor: {
                      include: {
                        vendorSubaccount: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!payment) {
      throw new NotFoundResponse("Payment not found");
    }

    // Get the vendor from the first product (orders are limited to single vendor)
    const vendor = payment.order.orderToProduct[0]?.product.vendor;
    if (!vendor || !vendor.vendorSubaccount) {
      throw new BadResponse("Vendor subaccount not found");
    }

    // Calculate 90% payout amount
    const payoutAmount = payment.amount * 0.9;
    const payoutAmountInKobo = Math.round(payoutAmount * 100);

    const transferParams = {
      source: "balance",
      amount: payoutAmountInKobo,
      recipient: vendor.vendorSubaccount.subaccountCode,
      reason: `Payout for order #${payment.order.id}`,
      reference: `payout_${payment.id}_${Date.now()}`,
    };

    const transferResponse = await paystackClient.post("/transfer", transferParams);

    if (!transferResponse.data.status) {
      throw new BadResponse("Failed to initiate payout");
    }

    // Create payout record
    const payout = await prisma.payout.create({
      data: {
        transferCode: transferResponse.data.data.transfer_code,
        amount: payoutAmount,
        paymentId,
        vendorId: vendor.id,
        reason: transferParams.reason,
        transferReference: transferResponse.data.data.reference,
      },
    });

    return { payout, transferData: transferResponse.data.data };
  } catch (error) {
    console.error("Error initiating payout:", error);
    throw error;
  }
}

/**
 * Verify webhook signature
 */
function verifyWebhookSignature(payload: string, signature: string): boolean {
  const hash = crypto
    .createHmac("sha512", process.env.PAYSTACK_SECRET_KEY!)
    .update(payload)
    .digest("hex");

  return hash === signature;
}

/**
 * Handle webhook events
 */
async function handleWebhookEvent(event: any) {
  try {
    switch (event.event) {
      case "charge.success":
        await handleChargeSuccess(event.data);
        break;
      case "transfer.success":
        await handleTransferSuccess(event.data);
        break;
      case "transfer.failed":
        await handleTransferFailed(event.data);
        break;
      case "transfer.reversed":
        await handleTransferReversed(event.data);
        break;
      default:
        console.log(`Unhandled webhook event: ${event.event}`);
    }
  } catch (error) {
    console.error("Error handling webhook event:", error);
    throw error;
  }
}

async function handleChargeSuccess(data: any) {
  const reference = data.reference;
  
  // Update payment status if not already updated
  const payment = await prisma.payment.findFirst({
    where: { reference },
  });

  if (payment && payment.status === "PENDING") {
    await verifyPayment(reference);
  }
}

async function handleTransferSuccess(data: any) {
  const transferCode = data.transfer_code;
  
  await prisma.payout.updateMany({
    where: { transferCode },
    data: {
      status: "SUCCESS",
      transferredAt: new Date(),
    },
  });
}

async function handleTransferFailed(data: any) {
  const transferCode = data.transfer_code;
  
  await prisma.payout.updateMany({
    where: { transferCode },
    data: {
      status: "FAILED",
    },
  });
}

async function handleTransferReversed(data: any) {
  const transferCode = data.transfer_code;
  
  await prisma.payout.updateMany({
    where: { transferCode },
    data: {
      status: "REVERSED",
    },
  });
}

export {
  initializePayment,
  verifyPayment,
  initiatePayout,
  verifyWebhookSignature,
  handleWebhookEvent,
};
