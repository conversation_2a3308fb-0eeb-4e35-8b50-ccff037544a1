/**
 * Payment Integration Tests
 * 
 * These tests demonstrate the payment flow and can be used for manual testing.
 * For production, consider using a proper testing framework like Jest.
 */

import { initializePayment, verifyPayment } from "~/services/payments";
import { createVendorSubaccount, getBanks } from "~/services/vendor-subaccounts";
import { prisma } from "~/lib/prisma";

/**
 * Test data for payment scenarios
 */
const testData = {
  user: {
    email: "<EMAIL>",
    name: "Test User"
  },
  vendor: {
    name: "Test Vendor",
    accountNumber: "0*********",
    bankCode: "011", // First Bank of Nigeria (test)
    bankName: "First Bank of Nigeria"
  },
  order: {
    totalPrice: 1000.00,
    products: [
      {
        name: "Eco-friendly Product",
        price: 1000.00,
        quantity: 1
      }
    ]
  }
};

/**
 * Test 1: Initialize Payment
 */
async function testInitializePayment() {
  console.log("🧪 Testing Payment Initialization...");
  
  try {
    // Create a test order first (simplified)
    const order = await prisma.order.create({
      data: {
        totalPrice: testData.order.totalPrice,
        userId: "test_user_id", // Replace with actual user ID
      }
    });

    const result = await initializePayment({
      orderId: order.id,
      email: testData.user.email,
      amount: testData.order.totalPrice
    });

    console.log("✅ Payment initialized successfully:");
    console.log(`- Reference: ${result.reference}`);
    console.log(`- Authorization URL: ${result.authorizationUrl}`);
    console.log(`- Access Code: ${result.accessCode}`);

    return result;
  } catch (error) {
    console.error("❌ Payment initialization failed:", error);
    throw error;
  }
}

/**
 * Test 2: Verify Payment
 */
async function testVerifyPayment(reference: string) {
  console.log("🧪 Testing Payment Verification...");
  
  try {
    const result = await verifyPayment(reference);

    console.log("✅ Payment verified successfully:");
    console.log(`- Status: ${result.payment.status}`);
    console.log(`- Amount: ${result.payment.amount}`);
    console.log(`- Paid At: ${result.payment.paidAt}`);

    return result;
  } catch (error) {
    console.error("❌ Payment verification failed:", error);
    throw error;
  }
}

/**
 * Test 3: Vendor Subaccount Creation
 */
async function testVendorSubaccount() {
  console.log("🧪 Testing Vendor Subaccount Creation...");
  
  try {
    // First, get available banks
    const banks = await getBanks();
    console.log(`📋 Found ${banks.length} banks available`);

    // Create vendor subaccount
    const result = await createVendorSubaccount({
      vendorId: "test_vendor_id", // Replace with actual vendor ID
      accountNumber: testData.vendor.accountNumber,
      bankCode: testData.vendor.bankCode,
      bankName: testData.vendor.bankName
    });

    console.log("✅ Vendor subaccount created successfully:");
    console.log(`- Subaccount Code: ${result.vendorSubaccount.subaccountCode}`);
    console.log(`- Account Name: ${result.vendorSubaccount.accountName}`);
    console.log(`- Status: ${result.vendorSubaccount.status}`);

    return result;
  } catch (error) {
    console.error("❌ Vendor subaccount creation failed:", error);
    throw error;
  }
}

/**
 * Test 4: Webhook Event Simulation
 */
function testWebhookEvent() {
  console.log("🧪 Testing Webhook Event Handling...");
  
  const sampleWebhookEvent = {
    event: "charge.success",
    data: {
      id: *********,
      reference: "ecobuilt_test_reference",
      amount: 100000, // Amount in kobo (1000 ZAR)
      currency: "ZAR",
      status: "success",
      paid_at: new Date().toISOString(),
      customer: {
        email: testData.user.email
      }
    }
  };

  console.log("📨 Sample webhook event:");
  console.log(JSON.stringify(sampleWebhookEvent, null, 2));
  
  console.log("✅ Webhook event structure is valid");
  return sampleWebhookEvent;
}

/**
 * Test 5: Error Scenarios
 */
async function testErrorScenarios() {
  console.log("🧪 Testing Error Scenarios...");
  
  const errorTests = [
    {
      name: "Invalid Order ID",
      test: () => initializePayment({
        orderId: "invalid_order_id",
        email: testData.user.email,
        amount: 100
      })
    },
    {
      name: "Invalid Email",
      test: () => initializePayment({
        orderId: "valid_order_id",
        email: "invalid-email",
        amount: 100
      })
    },
    {
      name: "Invalid Payment Reference",
      test: () => verifyPayment("invalid_reference")
    }
  ];

  for (const errorTest of errorTests) {
    try {
      await errorTest.test();
      console.log(`❌ ${errorTest.name}: Should have failed but didn't`);
    } catch (error) {
      console.log(`✅ ${errorTest.name}: Correctly handled error`);
    }
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log("🚀 Starting Payment Integration Tests...\n");
  
  try {
    // Test 1: Initialize Payment
    const paymentResult = await testInitializePayment();
    console.log("");

    // Test 2: Verify Payment (with test reference)
    // Note: In real scenario, this would be called after user completes payment
    console.log("ℹ️  Skipping payment verification (requires actual payment completion)");
    console.log("");

    // Test 3: Vendor Subaccount
    await testVendorSubaccount();
    console.log("");

    // Test 4: Webhook Event
    testWebhookEvent();
    console.log("");

    // Test 5: Error Scenarios
    await testErrorScenarios();
    console.log("");

    console.log("🎉 All tests completed successfully!");
    
  } catch (error) {
    console.error("💥 Test suite failed:", error);
  }
}

/**
 * Manual test runner
 * Uncomment the line below to run tests
 */
// runAllTests();

export {
  testInitializePayment,
  testVerifyPayment,
  testVendorSubaccount,
  testWebhookEvent,
  testErrorScenarios,
  runAllTests
};
