import { Router } from "express";
import {
  getAnalytics,
  getPaymentsController,
  getPayoutsController,
  getPaymentDetailsController,
  triggerPayoutController,
} from "~/controllers/admin/payments";

const paymentsRouter = Router();

// Get payment analytics
paymentsRouter.get("/analytics", getAnalytics);

// Get all payments
paymentsRouter.get("/", getPaymentsController);

// Get all payouts
paymentsRouter.get("/payouts", getPayoutsController);

// Get payment details
paymentsRouter.get("/:id", getPaymentDetailsController);

// Trigger manual payout
paymentsRouter.post("/:id/payout", triggerPayoutController);

export { paymentsRouter };
