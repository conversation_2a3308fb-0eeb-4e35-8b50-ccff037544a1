import { Router } from "express";
import {
  getBanks<PERSON><PERSON>roller,
  resolveAccountController,
  createSubaccountController,
  updateSubaccountController,
  getSubaccountController,
  deactivateSubaccountController,
} from "~/controllers/vendor/subaccounts";

const subaccountsRouter = Router();

// Get list of South African banks
subaccountsRouter.get("/banks", getBanksController);

// Resolve account number to get account name
subaccountsRouter.post("/resolve-account", resolveAccountController);

// Get vendor subaccount details
subaccountsRouter.get("/", getSubaccountController);

// Create vendor subaccount
subaccountsRouter.post("/", createSubaccountController);

// Update vendor subaccount
subaccountsRouter.put("/", updateSubaccountController);

// Deactivate vendor subaccount
subaccountsRouter.delete("/", deactivateSubaccountController);

export { subaccountsRouter };
