import { Router } from "express";

import { categoriesRouter } from "~/routers/vendor/categories";
import { dashboardRouter } from "~/routers/vendor/dashboard";
import { ordersRouter } from "~/routers/vendor/orders";
import { paymentsRouter } from "~/routers/vendor/payments";
import { productRequestsRouter } from "~/routers/vendor/product-requests";
import { productsRouter } from "~/routers/vendor/products";
import { profileRouter } from "~/routers/vendor/profile";
import { subaccountsRouter } from "~/routers/vendor/subaccounts";
import { usersRouter } from "~/routers/vendor/users";

const vendorRouter = Router();

vendorRouter.use("/dashboard", dashboardRouter);
vendorRouter.use("/categories", categoriesRouter);
vendorRouter.use("/orders", ordersRouter);
vendorRouter.use("/payments", paymentsRouter);
vendorRouter.use("/product-requests", productRequestsRouter);
vendorRouter.use("/products", productsRouter);
vendorRouter.use("/profile", profileRouter);
vendorRouter.use("/subaccounts", subaccountsRouter);
vendorRouter.use("/users", usersRouter);

export { vendorRouter };
