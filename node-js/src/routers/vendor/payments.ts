import { Router } from "express";
import {
  getAnalytics,
  getPayoutsController,
  getPaymentHistoryController,
  getPayoutDetailsController,
} from "~/controllers/vendor/payments";

const paymentsRouter = Router();

// Get payment analytics
paymentsRouter.get("/analytics", getAnalytics);

// Get vendor's payouts
paymentsRouter.get("/payouts", getPayoutsController);

// Get vendor's payment history
paymentsRouter.get("/history", getPaymentHistoryController);

// Get payout details
paymentsRouter.get("/payouts/:id", getPayoutDetailsController);

export { paymentsRouter };
