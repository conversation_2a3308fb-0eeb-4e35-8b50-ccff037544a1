import { Router } from "express";

import { createOrder, getOrder, getOrders } from "~/controllers/user/orders";
import { initializeOrderPayment } from "~/controllers/user/payments";

const ordersRouter = Router();

ordersRouter.get("/", getOrders);

ordersRouter.get("/:id", getOrder);

ordersRouter.post("/", createOrder);

// Initialize payment for an order
ordersRouter.post("/:id/payment", initializeOrderPayment);

export { ordersRouter };
