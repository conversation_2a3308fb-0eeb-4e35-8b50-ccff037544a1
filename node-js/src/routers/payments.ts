import { Router } from "express";
import {
  initializePaymentController,
  verifyPaymentController,
  webhookController,
} from "~/controllers/payments";

const paymentsRouter = Router();

// Initialize payment for an order
paymentsRouter.post("/initialize", initializePaymentController);

// Verify payment status
paymentsRouter.get("/verify/:reference", verifyPaymentController);

// Paystack webhook endpoint
paymentsRouter.post("/webhook", webhookController);

export { paymentsRouter };
