import { Router } from "express";
import {
  initializePaymentController,
  verifyPaymentController,
  webhookController,
} from "~/controllers/payments";
import {
  verifyPaystackWebhook,
  preventDuplicateWebhook,
  logWebhookEvent,
} from "~/middlewares/webhook";

const paymentsRouter = Router();

// Initialize payment for an order
paymentsRouter.post("/initialize", initializePaymentController);

// Verify payment status
paymentsRouter.get("/verify/:reference", verifyPaymentController);

// Paystack webhook endpoint with security middleware
paymentsRouter.post(
  "/webhook",
  verifyPaystackWebhook,
  logWebhookEvent,
  preventDuplicateWebhook,
  webhookController
);

export { paymentsRouter };
