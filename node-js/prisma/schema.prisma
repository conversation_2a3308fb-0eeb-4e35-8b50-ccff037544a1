generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum Role {
  UNSPECIFIED
  SUPER_ADMIN
  ADMIN
  VENDOR
  LOGISTIC
  USER
}

enum UserStatus {
  PENDING
  APPROVED
  REJECTED
}

model Auth {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  email      String     @unique
  password   String
  status     UserStatus @default(PENDING)
  role       Role       @default(UNSPECIFIED)
  isVerified Boolean    @default(false) @map("is_verified")
  isDeleted  Boolean    @default(false) @map("is_deleted")

  otpId String? @map("otp_id") @db.ObjectId
  otp   Otp?

  conversations ConversationToAuth[]
  messages      Message[]

  admin    Admin?
  vendor   Vendor?
  logistic LogisticProvider?
  user     User?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([role])
  @@index([status])
  @@index([isDeleted])
  @@index([createdAt])
}

enum OtpType {
  VERIFY
  RESET
}

model Otp {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  code String
  type OtpType @default(VERIFY)

  authId String @unique @map("auth_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([type])
  @@index([createdAt])
}

model Admin {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureId String @map("picture_id")
  name      String
  phone     String

  authId String @unique @map("auth_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([name])
  @@index([phone])
}

model Vendor {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureId     String @map("picture_id")
  name          String
  description   String
  phone         String
  postalCode    String @map("postal_code")
  city          String
  pickupAddress String @map("pickup_address")

  authId String @unique @map("auth_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  products         Product[]
  vendorSubaccount VendorSubaccount?
  payouts          Payout[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([name])
  @@index([city])
  @@index([postalCode])
  @@index([phone])
}

model LogisticProvider {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureId   String @map("picture_id")
  name        String
  description String
  phone       String
  postalCode  String @map("postal_code")
  city        String
  address     String @map("address")

  authId String @unique @map("auth_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  responses  LogisticProviderResponse[]
  deliveries DeliveryRequest[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([name])
  @@index([city])
  @@index([postalCode])
  @@index([phone])
}

model User {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureId       String @map("picture_id")
  name            String
  phone           String
  postalCode      String @map("postal_code")
  city            String
  deliveryAddress String @map("delivery_address")

  authId String @unique @map("auth_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  productRequests ProductRequest[]
  orders          Order[]
  reviews         Review[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([name])
  @@index([city])
  @@index([postalCode])
  @@index([phone])
}

enum CategoryStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ProductCondition {
  EXCELLENT
  GOOD
  FAIR
}

model Category {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  name      String
  status    CategoryStatus @default(PENDING)
  isDeleted Boolean        @default(false) @map("is_deleted")

  products        Product[]
  productRequests ProductRequest[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([name])
  @@index([status])
  @@index([isDeleted])
}

model Product {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureIds    String[]         @map("picture_ids")
  name          String
  description   String
  previousUsage String?          @map("previous_usage")
  sku           String           @unique
  stock         Int
  price         Float
  salePrice     Float?           @map("sale_price")
  condition     ProductCondition @default(GOOD)
  isVerified    Boolean          @default(false) @map("is_ecobuilt_verified")
  isDeleted     Boolean          @default(false) @map("is_deleted")

  categoryId String   @map("category_id") @db.ObjectId
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Restrict, onUpdate: Cascade)

  vendorId String @map("vendor_id") @db.ObjectId
  vendor   Vendor @relation(fields: [vendorId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  productRequestId String?         @map("product_request_id") @db.ObjectId
  productRequest   ProductRequest? @relation(fields: [productRequestId], references: [id], onDelete: SetNull, onUpdate: Cascade)

  orderToProduct OrderToProduct[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([name])
  @@index([categoryId])
  @@index([vendorId])
  @@index([productRequestId])
  @@index([price])
  @@index([condition])
  @@index([isVerified])
  @@index([isDeleted])
  @@index([stock])
  @@index([createdAt])
}

model ProductRequest {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  pictureIds  String[] @map("picture_ids")
  name        String
  description String
  quantity    Int
  price       Float
  isDeleted   Boolean  @default(false) @map("is_deleted")

  categoryId String   @map("category_id") @db.ObjectId
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Restrict, onUpdate: Cascade)

  userId String @map("user_id") @db.ObjectId
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  products Product[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([name])
  @@index([categoryId])
  @@index([userId])
  @@index([price])
  @@index([isDeleted])
  @@index([createdAt])
}

model OrderToProduct {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  quantity Int

  orderId String @map("order_id") @db.ObjectId
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  productId String  @map("product_id") @db.ObjectId
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@unique([orderId, productId])
  @@index([orderId])
  @@index([productId])
}

enum OrderStatus {
  PENDING
  PROCESSING
  READY
  COMPLETED
}

enum DeliveryOption {
  SELF_PICKUP
  LOGISTIC
}

model Order {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  totalPrice     Float          @map("total_price")
  status         OrderStatus    @default(PENDING)
  deliveryOption DeliveryOption @default(SELF_PICKUP) @map("delivery_option")

  userId String @map("user_id") @db.ObjectId
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  deliveryRequest DeliveryRequest?
  review          Review?
  payment         Payment?

  orderToProduct OrderToProduct[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([userId])
  @@index([status])
  @@index([deliveryOption])
  @@index([totalPrice])
  @@index([createdAt])
}

enum DeliveryRequestStatus {
  PENDING
  PROPOSED
  PROCESSING
  IN_TRANSIT
  DELIVERED
}

model DeliveryRequest {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  price         Float
  acceptedPrice Float?                @map("accepted_price")
  status        DeliveryRequestStatus @default(PENDING)

  orderId String @unique @map("order_id") @db.ObjectId
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  acceptedLogisticProviderId String?           @map("accepted_logistic_provider_id") @db.ObjectId
  acceptedLogisticProvider   LogisticProvider? @relation(fields: [acceptedLogisticProviderId], references: [id], onDelete: SetNull, onUpdate: Cascade)

  responses LogisticProviderResponse[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([status])
  @@index([acceptedLogisticProviderId])
  @@index([createdAt])
}

model LogisticProviderResponse {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  price Float

  deliveryRequestId String          @map("delivery_request_id") @db.ObjectId
  deliveryRequest   DeliveryRequest @relation(fields: [deliveryRequestId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  logisticProviderId String           @map("logistic_provider_id") @db.ObjectId
  logisticProvider   LogisticProvider @relation(fields: [logisticProviderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@unique([deliveryRequestId, logisticProviderId])
  @@index([deliveryRequestId])
  @@index([logisticProviderId])
  @@index([price])
}

model Review {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  rating  Int
  comment String

  orderId String @unique @map("order_id") @db.ObjectId
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  userId String @map("user_id") @db.ObjectId
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([userId])
  @@index([rating])
  @@index([createdAt])
}

// Payment-related models for Paystack integration

enum VendorSubaccountStatus {
  PENDING
  ACTIVE
  INACTIVE
}

model VendorSubaccount {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  subaccountCode String                 @map("subaccount_code")
  accountNumber  String                 @map("account_number")
  bankCode       String                 @map("bank_code")
  bankName       String                 @map("bank_name")
  accountName    String                 @map("account_name")
  status         VendorSubaccountStatus @default(PENDING)

  vendorId String @unique @map("vendor_id") @db.ObjectId
  vendor   Vendor @relation(fields: [vendorId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([subaccountCode])
  @@index([status])
  @@index([createdAt])
}

enum PaymentStatus {
  PENDING
  SUCCESS
  FAILED
  ABANDONED
}

model Payment {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  reference         String
  amount            Float
  currency          String        @default("ZAR")
  status            PaymentStatus @default(PENDING)
  paystackReference String?       @map("paystack_reference")
  accessCode        String?       @map("access_code")
  authorizationUrl  String?       @map("authorization_url")
  paidAt            DateTime?     @map("paid_at")

  orderId String @unique @map("order_id") @db.ObjectId
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  payouts Payout[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([reference])
  @@index([status])
  @@index([paidAt])
  @@index([createdAt])
}

enum PayoutStatus {
  PENDING
  SUCCESS
  FAILED
  REVERSED
}

model Payout {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  transferCode      String       @map("transfer_code")
  amount            Float
  currency          String       @default("ZAR")
  status            PayoutStatus @default(PENDING)
  reason            String?
  transferReference String?      @map("transfer_reference")
  transferredAt     DateTime?    @map("transferred_at")

  paymentId String  @map("payment_id") @db.ObjectId
  payment   Payment @relation(fields: [paymentId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  vendorId String @map("vendor_id") @db.ObjectId
  vendor   Vendor @relation(fields: [vendorId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([transferCode])
  @@index([status])
  @@index([paymentId])
  @@index([vendorId])
  @@index([transferredAt])
  @@index([createdAt])
}

model WebhookEvent {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  eventId     String   @map("event_id")
  eventType   String   @map("event_type")
  data        Json
  processedAt DateTime @map("processed_at")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([eventId])
  @@index([eventType])
  @@index([processedAt])
  @@index([createdAt])
}

model ConversationToAuth {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  conversationId String       @map("conversation_id") @db.ObjectId
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  authId String @map("auth_id") @db.ObjectId
  auth   Auth   @relation(fields: [authId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@unique([conversationId, authId])
  @@index([conversationId])
  @@index([authId])
}

enum ConversationType {
  VENDOR
  LOGISTIC
}

enum ConversationEndedBy {
  SYSTEM
  ADMIN
}

model Conversation {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  referenceId String @unique @map("reference_id") @db.ObjectId

  endedBy ConversationEndedBy? @map("ended_by")
  type    ConversationType     @map("type")

  members ConversationToAuth[]

  messages Message[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([type])
  @@index([endedBy])
  @@index([createdAt])
}

model Message {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  content String
  isRead  Boolean @default(false) @map("is_read")

  senderId String @map("sender_id") @db.ObjectId
  sender   Auth   @relation(fields: [senderId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  conversationId String       @map("conversation_id") @db.ObjectId
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([senderId])
  @@index([conversationId])
  @@index([isRead])
  @@index([createdAt])
}
