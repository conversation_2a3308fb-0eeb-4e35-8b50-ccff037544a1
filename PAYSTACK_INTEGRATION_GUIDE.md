# Paystack Integration Guide - EcoBuiltConnect

## Overview

This document provides a comprehensive guide to the Paystack payment integration implemented in EcoBuiltConnect, a South African marketplace for eco-friendly products.

## Architecture

### Payment Flow
1. **User places order** → Order created with PENDING status
2. **User initiates payment** → Paystack payment initialized
3. **User completes payment** → Webhook triggers payment verification
4. **Payment verified** → Order status updated to PROCESSING
5. **Automatic payout** → 90% of payment sent to vendor, 10% retained as admin commission

### Key Components

#### Backend (Node.js/Express)
- **Payment Services** (`src/services/payments.ts`)
- **Vendor Subaccount Services** (`src/services/vendor-subaccounts.ts`)
- **Admin Payment Services** (`src/services/admin/payments.ts`)
- **Webhook Middleware** (`src/middlewares/webhook.ts`)

#### Frontend (Next.js/React)
- **Paystack Payment Component** (`src/components/payments/paystack-payment.tsx`)
- **Checkout Component** (`src/components/payments/checkout.tsx`)
- **Bank Account Setup** (`src/components/vendor/bank-account-setup.tsx`)

#### Database Models
- **Payment** - Tracks payment transactions
- **Payout** - Tracks vendor payouts
- **VendorSubaccount** - Stores vendor banking details
- **WebhookEvent** - Prevents duplicate webhook processing

## Environment Variables

### Backend (.env)
```
PAYSTACK_SECRET_KEY=sk_test_your_secret_key
PAYSTACK_PUBLIC_KEY=pk_test_your_public_key
```

### Frontend (.env)
```
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_your_public_key
```

## API Endpoints

### Public Payment Routes
- `POST /payments/initialize` - Initialize payment
- `GET /payments/verify/:reference` - Verify payment
- `POST /payments/webhook` - Paystack webhook handler

### User Routes
- `POST /user/orders/:id/payment` - Initialize payment for order

### Vendor Routes
- `GET /vendor/subaccounts/banks` - Get list of banks
- `POST /vendor/subaccounts/resolve-account` - Resolve account details
- `GET /vendor/subaccounts` - Get vendor subaccount
- `POST /vendor/subaccounts` - Create vendor subaccount
- `PUT /vendor/subaccounts` - Update vendor subaccount
- `GET /vendor/payments/analytics` - Get payment analytics
- `GET /vendor/payments/payouts` - Get vendor payouts
- `GET /vendor/payments/history` - Get payment history

### Admin Routes
- `GET /admin/payments/analytics` - Get payment analytics
- `GET /admin/payments` - Get all payments
- `GET /admin/payments/payouts` - Get all payouts
- `GET /admin/payments/:id` - Get payment details
- `POST /admin/payments/:id/payout` - Trigger manual payout

## Usage Examples

### 1. Vendor Onboarding
```typescript
// Frontend - Bank Account Setup
import { BankAccountSetup } from "~/components/vendor/bank-account-setup";

function VendorSettings() {
  return (
    <div>
      <h1>Payment Settings</h1>
      <BankAccountSetup />
    </div>
  );
}
```

### 2. Order Checkout
```typescript
// Frontend - Checkout Flow
import { Checkout } from "~/components/payments/checkout";

function OrderCheckout({ order, user }) {
  const handlePaymentSuccess = () => {
    // Redirect to order confirmation
    router.push(`/orders/${order.id}/confirmation`);
  };

  return (
    <Checkout
      orderId={order.id}
      orderTotal={order.totalPrice}
      userEmail={user.email}
      onPaymentSuccess={handlePaymentSuccess}
      onPaymentCancel={() => router.back()}
    />
  );
}
```

### 3. Backend Payment Processing
```typescript
// Backend - Initialize Payment
import { initializePayment } from "~/services/payments";

const result = await initializePayment({
  orderId: "order_id",
  email: "<EMAIL>",
  amount: 1000.00
});

// Returns: { payment, authorizationUrl, accessCode, reference }
```

## Webhook Configuration

### Paystack Dashboard Setup
1. Go to Paystack Dashboard → Settings → Webhooks
2. Add webhook URL: `https://yourdomain.com/payments/webhook`
3. Select events:
   - `charge.success`
   - `transfer.success`
   - `transfer.failed`
   - `transfer.reversed`

### Webhook Security
- Signature verification using HMAC SHA512
- Duplicate event prevention
- Event logging for debugging
- Async event processing

## Security Features

### Payment Security
- Server-side payment verification
- Webhook signature validation
- Duplicate transaction prevention
- Secure API key management

### Data Protection
- Sensitive data encryption
- PCI DSS compliance through Paystack
- Secure webhook endpoints
- Rate limiting on payment endpoints

## Error Handling

### Common Error Scenarios
1. **Invalid payment reference** - Returns 404 with clear message
2. **Webhook signature mismatch** - Returns 400, logs security event
3. **Vendor subaccount not found** - Prevents payout, notifies admin
4. **Network timeout** - Retries with exponential backoff
5. **Insufficient balance** - Graceful failure with user notification

### Error Response Format
```json
{
  "success": false,
  "message": "Human-readable error message",
  "error": {
    "code": "ERROR_CODE",
    "details": "Technical details for debugging"
  }
}
```

## Testing

### Test Environment
- Use Paystack test keys for development
- Test webhook endpoints with ngrok for local development
- Verify all payment flows in test mode

### Test Scenarios
1. **Successful payment flow**
2. **Failed payment handling**
3. **Webhook duplicate prevention**
4. **Vendor payout processing**
5. **Error scenarios and recovery**

## Monitoring and Logging

### Key Metrics to Monitor
- Payment success rate
- Payout processing time
- Webhook delivery success
- Error rates by endpoint
- Revenue and commission tracking

### Logging Strategy
- All payment events logged with correlation IDs
- Webhook events logged with processing status
- Error events logged with stack traces
- Performance metrics for payment flows

## Troubleshooting

### Common Issues

#### Payment Not Processing
1. Check Paystack API keys
2. Verify webhook URL accessibility
3. Check payment reference format
4. Validate order status

#### Payout Failures
1. Verify vendor subaccount setup
2. Check Paystack balance
3. Validate bank account details
4. Review transfer recipient status

#### Webhook Issues
1. Verify webhook signature
2. Check endpoint accessibility
3. Review event processing logs
4. Validate event data format

## Maintenance

### Regular Tasks
- Monitor payment success rates
- Review failed transactions
- Update Paystack API integration
- Backup payment data
- Review security logs

### Updates and Migrations
- Test all changes in staging environment
- Coordinate with Paystack for API updates
- Maintain backward compatibility
- Document all changes

## Support

For technical issues:
1. Check application logs
2. Review Paystack dashboard
3. Verify webhook delivery
4. Contact Paystack support if needed

For integration questions:
- Review this documentation
- Check Paystack API documentation
- Test in sandbox environment first
